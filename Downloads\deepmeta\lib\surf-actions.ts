"use server";

import { Sandbox } from "@e2b/desktop";

const SANDBOX_TIMEOUT_MS = 5 * 60 * 1000; // 5 minutes

export async function createSandbox(resolution: [number, number] = [1024, 768]) {
  try {
    const sandbox = await Sandbox.create({
      resolution,
      dpi: 96,
      timeoutMs: SANDBOX_TIMEOUT_MS,
    });

    await sandbox.stream.start();

    return {
      success: true,
      sandboxId: sandbox.sandboxId,
      vncUrl: sandbox.stream.getUrl(),
    };
  } catch (error) {
    console.error("Failed to create sandbox:", error);
    return {
      success: false,
      error: "Failed to create sandbox",
    };
  }
}

export async function increaseTimeout(sandboxId: string) {
  try {
    const desktop = await Sandbox.connect(sandboxId);
    await desktop.setTimeout(SANDBOX_TIMEOUT_MS);
    return { success: true };
  } catch (error) {
    console.error("Failed to increase timeout:", error);
    return { success: false, error: "Failed to increase timeout" };
  }
}

export async function stopSandbox(sandboxId: string) {
  try {
    const desktop = await Sandbox.connect(sandboxId);
    await desktop.kill();
    return { success: true };
  } catch (error) {
    console.error("Failed to stop sandbox:", error);
    return { success: false, error: "Failed to stop sandbox" };
  }
}

export async function getSandboxStatus(sandboxId: string) {
  try {
    const desktop = await Sandbox.connect(sandboxId);
    // Basic health check
    await desktop.screenshot();
    return { success: true, status: "running" };
  } catch (error) {
    console.error("Sandbox not accessible:", error);
    return { success: false, status: "stopped", error: String(error) };
  }
}
