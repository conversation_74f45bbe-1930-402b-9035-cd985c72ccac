import { Sandbox } from "@e2b/desktop";
import OpenA<PERSON> from "openai";
import { SSEEventType, SSEEvent, ActionResponse } from "./surf-types";
import { ComputerStreamerBase, StreamProps, ResolutionScaler } from "./surf-streaming";

const INSTRUCTIONS = `
You are Surf, a helpful assistant that can use a computer to help the user with their tasks.
You can use the computer to search the web, write code, and more.

Surf is built by E2B, which provides an open source isolated virtual computer in the cloud made for AI use cases.
This application integrates E2B's desktop sandbox with OpenAI's API to create an AI agent that can perform tasks
on a virtual computer through natural language instructions.

The screenshots that you receive are from a running sandbox instance, allowing you to see and interact with a real
virtual computer environment in real-time.

Since you are operating in a secure, isolated sandbox micro VM, you can execute most commands and operations without
worrying about security concerns. This environment is specifically designed for AI experimentation and task execution.

The sandbox is based on Ubuntu 22.04 and comes with many pre-installed applications including:
- Firefox browser
- Visual Studio Code
- LibreOffice suite
- Python 3 with common libraries
- Terminal with standard Linux utilities
- File manager (PCManFM)
- Text editor (Gedit)
- Calculator and other basic utilities

IMPORTANT: It is okay to run terminal commands at any point without confirmation, as long as they are required to fulfill the task the user has given. You should execute commands immediately when needed to complete the user's request efficiently.

IMPORTANT: When typing commands in the terminal, ALWAYS send a KEYPRESS ENTER action immediately after typing the command to execute it. Terminal commands will not run until you press Enter.

IMPORTANT: When editing files, prefer to use Visual Studio Code (VS Code) as it provides a better editing experience with syntax highlighting, code completion, and other helpful features.
`;

export class OpenAIComputerStreamer extends ComputerStreamerBase {
  instructions = INSTRUCTIONS;
  private openai: OpenAI;

  constructor(
    public desktop: Sandbox,
    private resolutionScaler: ResolutionScaler
  ) {
    super();
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
  }

  async *stream({ messages, signal }: StreamProps): AsyncGenerator<SSEEvent> {
    try {
      // Take initial screenshot
      const screenshot = await this.resolutionScaler.takeScreenshot();
      
      // Prepare messages for OpenAI
      const openaiMessages = [
        {
          role: "system" as const,
          content: this.instructions,
        },
        {
          role: "user" as const,
          content: [
            {
              type: "text" as const,
              text: messages[messages.length - 1]?.content || "Please help me with this task.",
            },
            {
              type: "image_url" as const,
              image_url: {
                url: `data:image/png;base64,${screenshot}`,
              },
            },
          ],
        },
      ];

      // Stream response from OpenAI
      const stream = await this.openai.chat.completions.create({
        model: "gpt-4o",
        messages: openaiMessages,
        stream: true,
        tools: [
          {
            type: "function",
            function: {
              name: "computer",
              description: "Use the computer to perform actions",
              parameters: {
                type: "object",
                properties: {
                  action: {
                    type: "string",
                    enum: ["screenshot", "click", "type", "key", "scroll"],
                  },
                  coordinate: {
                    type: "array",
                    items: { type: "number" },
                    minItems: 2,
                    maxItems: 2,
                  },
                  text: { type: "string" },
                },
                required: ["action"],
              },
            },
          },
        ],
      });

      for await (const chunk of stream) {
        if (signal.aborted) break;

        const delta = chunk.choices[0]?.delta;
        
        if (delta?.content) {
          yield {
            type: SSEEventType.MESSAGE_CHUNK,
            content: delta.content,
          };
        }

        if (delta?.tool_calls) {
          for (const toolCall of delta.tool_calls) {
            if (toolCall.function?.name === "computer") {
              try {
                const args = JSON.parse(toolCall.function.arguments || "{}");
                const result = await this.executeAction(args);
                
                yield {
                  type: SSEEventType.ACTION_EXECUTED,
                  action: args,
                };

                if (result?.screenshot) {
                  // Take new screenshot after action
                  const newScreenshot = await this.resolutionScaler.takeScreenshot();
                  yield {
                    type: SSEEventType.MESSAGE_CHUNK,
                    content: `\n\n[Action completed: ${args.action}]`,
                  };
                }
              } catch (error) {
                yield {
                  type: SSEEventType.ERROR,
                  error: `Failed to execute action: ${error}`,
                };
              }
            }
          }
        }
      }

      yield { type: SSEEventType.DONE };
    } catch (error) {
      yield {
        type: SSEEventType.ERROR,
        error: `Streaming error: ${error}`,
      };
    }
  }

  async executeAction(action: any): Promise<ActionResponse> {
    try {
      switch (action.action) {
        case "screenshot":
          const screenshot = await this.resolutionScaler.takeScreenshot();
          return { success: true, screenshot };

        case "click":
          if (action.coordinate) {
            const [x, y] = this.resolutionScaler.scaleCoordinates(
              action.coordinate[0],
              action.coordinate[1]
            );
            await this.desktop.mouse.click(x, y);
          }
          return { success: true };

        case "type":
          if (action.text) {
            await this.desktop.keyboard.type(action.text);
          }
          return { success: true };

        case "key":
          if (action.text) {
            await this.desktop.keyboard.press(action.text);
          }
          return { success: true };

        case "scroll":
          if (action.coordinate) {
            const [x, y] = action.coordinate;
            await this.desktop.mouse.scroll(x, y, 3);
          }
          return { success: true };

        default:
          return { success: false, error: "Unknown action" };
      }
    } catch (error) {
      return { success: false, error: String(error) };
    }
  }
}
