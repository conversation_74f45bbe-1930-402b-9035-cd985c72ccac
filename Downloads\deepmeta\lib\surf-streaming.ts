import { Sandbox } from "@e2b/desktop";
import { SSEEvent, ActionResponse, ComputerModel } from "./surf-types";

export function formatSSE(event: SSEEvent): string {
  return `data: ${JSON.stringify(event)}\n\n`;
}

export interface StreamProps {
  signal: AbortSignal;
  messages: { role: "user" | "assistant"; content: string }[];
}

export abstract class ComputerStreamerBase {
  abstract instructions: string;
  abstract desktop: Sandbox;

  abstract stream(props: StreamProps): AsyncGenerator<SSEEvent>;
  abstract executeAction(action: unknown): Promise<ActionResponse | void>;
}

export function createStreamingResponse(
  generator: AsyncGenerator<SSEEvent>
): Response {
  const stream = new ReadableStream({
    async start(controller) {
      for await (const chunk of generator) {
        controller.enqueue(new TextEncoder().encode(formatSSE(chunk)));
      }
      controller.close();
    },
  });

  return new Response(stream, {
    headers: {
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache",
      Connection: "keep-alive",
    },
  });
}

export class ResolutionScaler {
  constructor(
    private desktop: Sandbox,
    private targetResolution: [number, number]
  ) {}

  scaleCoordinates(x: number, y: number): [number, number] {
    // Simple scaling - in production you'd want more sophisticated scaling
    return [x, y];
  }

  async takeScreenshot(): Promise<string> {
    const screenshot = await this.desktop.screenshot();
    return screenshot;
  }
}
