import { NextResponse } from "next/server";
import { Sandbox } from "@e2b/desktop";
import { ComputerModel, SSEEvent, SSEEventType } from "@/lib/surf-types";
import { createStreamingResponse } from "@/lib/surf-streaming";
import { OpenAIComputerStreamer } from "@/lib/surf-openai-streamer";
import { ResolutionScaler } from "@/lib/surf-streaming";

export const maxDuration = 800;

const SANDBOX_TIMEOUT_MS = 5 * 60 * 1000; // 5 minutes

class StreamerFactory {
  static getStreamer(
    model: ComputerModel,
    desktop: Sandbox,
    resolution: [number, number]
  ) {
    const resolutionScaler = new ResolutionScaler(desktop, resolution);

    switch (model) {
      case "anthropic":
        // Currently not implemented
        throw new Error("Anthropic model not yet implemented");
      case "openai":
      default:
        return new OpenAIComputerStreamer(desktop, resolutionScaler);
    }
  }
}

export async function POST(request: Request) {
  const abortController = new AbortController();
  const { signal } = abortController;

  request.signal.addEventListener("abort", () => {
    abortController.abort();
  });

  try {
    const {
      messages,
      sandboxId,
      resolution = [1024, 768],
      model = "openai",
    } = await request.json();

    const apiKey = process.env.E2B_API_KEY;
    const openaiKey = process.env.OPENAI_API_KEY;

    if (!apiKey) {
      return NextResponse.json({ error: "E2B API key not found" }, { status: 500 });
    }

    if (!openaiKey) {
      return NextResponse.json({ error: "OpenAI API key not found" }, { status: 500 });
    }

    let desktop: Sandbox | undefined;
    let activeSandboxId = sandboxId;
    let vncUrl: string | undefined;

    // Create or connect to sandbox
    if (!activeSandboxId) {
      console.log("Creating new E2B desktop sandbox...");
      const newSandbox = await Sandbox.create({
        resolution,
        dpi: 96,
        timeoutMs: SANDBOX_TIMEOUT_MS,
      });

      await newSandbox.stream.start();

      activeSandboxId = newSandbox.sandboxId;
      vncUrl = newSandbox.stream.getUrl();
      desktop = newSandbox;
      
      console.log(`Created sandbox: ${activeSandboxId}`);
    } else {
      console.log(`Connecting to existing sandbox: ${activeSandboxId}`);
      desktop = await Sandbox.connect(activeSandboxId);
    }

    if (!desktop) {
      return NextResponse.json({ error: "Failed to connect to sandbox" }, { status: 500 });
    }

    desktop.setTimeout(SANDBOX_TIMEOUT_MS);

    try {
      const streamer = StreamerFactory.getStreamer(
        model as ComputerModel,
        desktop,
        resolution
      );

      if (!sandboxId && activeSandboxId && vncUrl) {
        async function* stream(): AsyncGenerator<SSEEvent> {
          yield {
            type: SSEEventType.SANDBOX_CREATED,
            sandboxId: activeSandboxId,
            vncUrl: vncUrl as string,
          };

          yield* streamer.stream({ messages, signal });
        }

        return createStreamingResponse(stream());
      } else {
        return createStreamingResponse(streamer.stream({ messages, signal }));
      }
    } catch (error) {
      console.error("Error from streaming service:", error);
      return NextResponse.json(
        { error: "An error occurred with the AI service. Please try again." },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error in Surf API:", error);
    return NextResponse.json(
      { error: "Failed to process request" },
      { status: 500 }
    );
  }
}
