:root {
  --bg: hsl(0 0% 100%);
  --bg-100: hsl(0 0% 98%);
  --bg-200: hsl(0 0% 96%);
  --bg-300: hsl(0 0% 94%);
  --bg-400: hsl(0 0% 92%);
  --bg-500: hsl(0 0% 90%);

  --fg: hsl(0 0% 5%);
  --fg-100: hsl(0 0% 13%);
  --fg-200: hsl(0 0% 21%);
  --fg-300: hsl(0 0% 29%);
  --fg-400: hsl(0 0% 37%);
  --fg-500: hsl(0 0% 45%);

  --accent: hsl(32 100% 50%);
  --accent-100: hsl(32 95% 55%);
  --accent-200: hsl(32 90% 60%);
  --accent-300: hsl(32 85% 65%);
  --accent-400: hsl(32 80% 70%);
  --accent-500: hsl(32 75% 75%);

  --accent-fg: hsl(45 20% 95%);

  --border: hsl(0 0% 90%);
  --border-100: hsl(0 0% 85%);
  --border-200: hsl(0 0% 80%);
  --border-300: hsl(0 0% 75%);
  --border-400: hsl(0 0% 70%);
  --border-500: hsl(0 0% 65%);

  --ring: hsl(32 85% 45%);

  --chart-1: hsl(32 85% 55%);
  --chart-2: hsl(212 85% 55%);
  --chart-3: hsl(152 85% 55%);
  --chart-4: hsl(272 85% 55%);
  --chart-5: hsl(332 85% 55%);

  --contrast-1: hsl(200 85% 50%);
  --contrast-2: hsl(235 86% 65%);

  --error: hsl(0 75% 50%);
  --error-100: hsl(0 75% 55%);
  --error-200: hsl(0 75% 60%);
  --error-300: hsl(0 75% 65%);
  --error-400: hsl(0 75% 70%);
  --error-500: hsl(0 75% 75%);

  --error-fg: hsl(45 20% 95%);

  --warning: hsl(35 80% 40%);
  --success: hsl(140 95% 30%);

  /* Commons */
  --radius: 0.375rem;

  --shadow: hsl(0 0% 85%);
  --shadow-strength: 0.2;
}

.dark {
  --bg: hsl(0 0% 3%);
  --bg-100: hsl(0 0% 5%);
  --bg-200: hsl(0 0% 7%);
  --bg-300: hsl(0 0% 9%);
  --bg-400: hsl(0 0% 11%);
  --bg-500: hsl(0 0% 13%);

  --fg: hsl(0 0% 98%);
  --fg-100: hsl(0 0% 90%);
  --fg-200: hsl(0 0% 82%);
  --fg-300: hsl(0 0% 74%);
  --fg-400: hsl(0 0% 66%);
  --fg-500: hsl(0 0% 60%);

  --accent: hsl(32 100% 45%);
  --accent-100: hsl(32 100% 40%);
  --accent-200: hsl(32 100% 30%);
  --accent-300: hsl(32 100% 20%);
  --accent-400: hsl(32 100% 10%);
  --accent-500: hsl(32 100% 5%);

  --accent-fg: hsl(0 0% 100%);

  --border: hsl(0 0% 10%);
  --border-100: hsl(0 0% 13%);
  --border-200: hsl(0 0% 16%);
  --border-300: hsl(0 0% 19%);
  --border-400: hsl(0 0% 22%);
  --border-500: hsl(0 0% 25%);

  --ring: hsl(32 100% 50%);

  --chart-1: hsl(220 70% 50%);
  --chart-2: hsl(160 60% 45%);
  --chart-3: hsl(30 80% 55%);
  --chart-4: hsl(280 65% 60%);
  --chart-5: hsl(340 75% 55%);

  --error: hsl(0 100% 65%);
  --error-100: hsl(0 90% 60%);
  --error-200: hsl(0 85% 55%);
  --error-300: hsl(0 80% 50%);
  --error-400: hsl(0 75% 45%);
  --error-500: hsl(0 70% 40%);

  --error-fg: hsl(0 0% 100%);

  --warning: hsl(60 100% 45%);
  --success: hsl(160 100% 43%);

  --contrast-1: hsl(200 85% 65%);
  --contrast-2: hsl(235 86% 75%);

  --shadow: hsl(0 0% 0%);
  --shadow-strength: 0.2;
}
