export type ComputerModel = "openai" | "anthropic";

export enum SSEEventType {
  SANDBOX_CREATED = "sandbox_created",
  MESSAGE_CHUNK = "message_chunk",
  ACTION_EXECUTED = "action_executed",
  ERROR = "error",
  DONE = "done",
}

export interface SSEEvent<T extends ComputerModel = ComputerModel> {
  type: SSEEventType;
  sandboxId?: string;
  vncUrl?: string;
  content?: string;
  action?: T extends "openai" ? OpenAIAction : AnthropicAction;
  error?: string;
}

export interface OpenAIAction {
  type: "computer";
  action: "screenshot" | "click" | "type" | "key" | "scroll";
  coordinate?: [number, number];
  text?: string;
}

export interface AnthropicAction {
  type: "computer";
  action: "screenshot" | "click" | "type" | "key" | "scroll";
  coordinate?: [number, number];
  text?: string;
}

export interface ActionResponse {
  success: boolean;
  screenshot?: string;
  error?: string;
}

export interface SurfMessage {
  id: string;
  role: "user" | "assistant";
  content: string;
  timestamp: number;
}

export interface SurfChatState {
  messages: SurfMessage[];
  sandboxId?: string;
  vncUrl?: string;
  isLoading: boolean;
  error?: string;
}
