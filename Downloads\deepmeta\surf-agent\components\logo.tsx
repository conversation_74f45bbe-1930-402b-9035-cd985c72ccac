export type LogoStyle = "e2b" | "fragments";

export default function Logo({
  style = "e2b",
  ...props
}: { style?: LogoStyle } & React.SVGProps<SVGSVGElement>) {
  return style === "fragments" ? (
    <svg
      {...props}
      viewBox="0 0 232 232"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M230.85 113.23L193.6 75.9799C192.87 75.2399 191.87 74.8299 190.83 74.8299H162.56C159.13 74.8299 157.17 72.8699 157.17 69.4399V41.1599C157.17 40.1199 156.76 39.1199 156.02 38.3899L118.77 1.14994C117.24 -0.380059 114.76 -0.380059 113.23 1.14994L75.9799 38.3999C75.2399 39.1399 74.8299 40.1299 74.8299 41.1699V69.4399C74.8299 72.8699 72.8699 74.8299 69.4399 74.8299H41.1699C40.1299 74.8299 39.1299 75.2399 38.3999 75.9799L1.14994 113.23C-0.380059 114.76 -0.380059 117.24 1.14994 118.77L38.3999 156.02C39.1399 156.75 40.1299 157.17 41.1699 157.17H69.4599C72.8799 157.17 74.8399 159.13 74.8399 162.56V190.83C74.8399 191.87 75.2499 192.87 75.9899 193.6L113.24 230.85C114.77 232.38 117.25 232.38 118.78 230.85L156.03 193.6C156.76 192.87 157.18 191.87 157.18 190.83V162.54C157.18 159.12 159.14 157.16 162.57 157.16H190.84C191.88 157.16 192.88 156.75 193.61 156.01L230.86 118.76C232.39 117.23 232.39 114.75 230.86 113.21L230.85 113.23ZM189.68 118.77L151.28 157.16L118.76 189.68C117.23 191.21 114.75 191.21 113.22 189.68L74.8199 151.28L42.2999 118.77C40.7699 117.24 40.7699 114.76 42.2999 113.22L80.6999 74.8199L113.22 42.2999C114.75 40.7699 117.23 40.7699 118.76 42.2999L157.16 80.6999L189.67 113.22C191.2 114.75 191.2 117.23 189.67 118.77H189.68Z"
        fill="currentColor"
      />
    </svg>
  ) : (
    <svg
      {...props}
      viewBox="0 0 224 232"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M188.212 157.998C186.672 157.998 185.71 159.665 186.48 160.998L202.585 188.894C203.476 190.437 202.056 192.287 200.335 191.826L151.491 178.737C149.357 178.165 147.163 179.432 146.592 181.566L133.504 230.411C133.042 232.132 130.731 232.436 129.84 230.893L113.732 202.992C112.962 201.659 111.037 201.659 110.268 202.992L94.1595 230.893C93.2686 232.436 90.9568 232.132 90.4956 230.411L77.4075 181.566C76.8357 179.432 74.6423 178.165 72.5085 178.737L23.664 191.826C21.9429 192.287 20.5234 190.437 21.4143 188.894L37.5192 160.998C38.289 159.665 37.3267 157.998 35.7871 157.998L3.57893 157.998C1.79713 157.998 0.904821 155.844 2.16476 154.584L37.9218 118.827C39.484 117.265 39.484 114.733 37.9218 113.171L2.16478 77.4133C0.904844 76.1533 1.7972 73.999 3.57902 73.9991L35.7837 73.9995C37.3233 73.9995 38.2856 72.3328 37.5158 70.9995L21.4143 43.11C20.5234 41.5669 21.9429 39.717 23.664 40.1781L72.5085 53.2665C74.6423 53.8383 76.8357 52.572 77.4075 50.4381L90.4956 1.59292C90.9568 -0.128187 93.2686 -0.432531 94.1595 1.11058L110.267 29.0111C111.037 30.3445 112.962 30.3445 113.732 29.0111L129.84 1.11058C130.73 -0.432532 133.042 -0.128189 133.503 1.59292L146.592 50.4381C147.163 52.572 149.357 53.8383 151.491 53.2665L200.335 40.1781C202.056 39.717 203.476 41.5669 202.585 43.11L186.483 70.9995C185.713 72.3328 186.676 73.9995 188.215 73.9995L220.421 73.9991C222.203 73.999 223.095 76.1533 221.835 77.4133L186.078 113.171C184.516 114.733 184.516 117.265 186.078 118.827L221.835 154.584C223.095 155.844 222.203 157.998 220.421 157.998L188.212 157.998ZM175.919 81.3306C177.366 79.8837 175.963 77.4549 173.987 77.9845L130.491 89.6396C128.357 90.2114 126.164 88.9451 125.592 86.8112L113.931 43.293C113.402 41.3166 110.597 41.3166 110.068 43.293L98.4069 86.8112C97.8351 88.9451 95.6418 90.2114 93.5079 89.6396L50.0136 77.9849C48.0371 77.4553 46.6348 79.8841 48.0817 81.331L79.9216 113.171C81.4837 114.733 81.4837 117.266 79.9216 118.828L48.0742 150.675C46.6273 152.122 48.0296 154.55 50.0061 154.021L93.5079 142.364C95.6418 141.792 97.8351 143.059 98.4069 145.192L110.068 188.711C110.597 190.687 113.402 190.687 113.931 188.711L125.592 145.192C126.164 143.059 128.357 141.792 130.491 142.364L173.994 154.021C175.971 154.551 177.373 152.122 175.926 150.675L144.079 118.828C142.516 117.266 142.516 114.733 144.079 113.171L175.919 81.3306Z"
        fill="currentColor"
      />
    </svg>
  );
}
