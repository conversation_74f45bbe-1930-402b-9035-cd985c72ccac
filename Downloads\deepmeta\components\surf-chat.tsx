"use client";

import React, { useState, useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Send, Monitor, Square, Clock, Loader2 } from "lucide-react";
import { SurfMessage, SurfChatState, SSEEventType } from "@/lib/surf-types";
import { createSandbox, stopSandbox, increaseTimeout } from "@/lib/surf-actions";
import { cn } from "@/lib/utils";

interface SurfChatProps {
  className?: string;
}

export default function SurfChat({ className }: SurfChatProps) {
  const [state, setState] = useState<SurfChatState>({
    messages: [
      {
        id: "welcome",
        role: "assistant",
        content: "Hello! I'm <PERSON><PERSON>, your AI computer assistant. I can help you with tasks on a virtual desktop. Click 'Start Sandbox' to begin!",
        timestamp: Date.now(),
      },
    ],
    isLoading: false,
  });
  
  const [input, setInput] = useState("");
  const [timeLeft, setTimeLeft] = useState<number | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [state.messages]);

  // Timer for sandbox timeout
  useEffect(() => {
    if (!state.sandboxId) return;

    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev === null) return 300; // 5 minutes
        if (prev <= 1) {
          handleExtendTimeout();
          return 300;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [state.sandboxId]);

  const handleStartSandbox = async () => {
    setState((prev) => ({ ...prev, isLoading: true }));
    
    try {
      const result = await createSandbox();
      
      if (result.success) {
        setState((prev) => ({
          ...prev,
          sandboxId: result.sandboxId,
          vncUrl: result.vncUrl,
          isLoading: false,
          messages: [
            ...prev.messages,
            {
              id: crypto.randomUUID(),
              role: "assistant",
              content: `🖥️ Sandbox created successfully! ID: ${result.sandboxId}\n\nYou can now give me instructions to perform tasks on the virtual desktop.`,
              timestamp: Date.now(),
            },
          ],
        }));
        setTimeLeft(300); // 5 minutes
      } else {
        setState((prev) => ({
          ...prev,
          isLoading: false,
          error: result.error,
        }));
      }
    } catch (error) {
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error: "Failed to create sandbox",
      }));
    }
  };

  const handleStopSandbox = async () => {
    if (!state.sandboxId) return;

    setState((prev) => ({ ...prev, isLoading: true }));
    
    try {
      await stopSandbox(state.sandboxId);
      setState((prev) => ({
        ...prev,
        sandboxId: undefined,
        vncUrl: undefined,
        isLoading: false,
        messages: [
          ...prev.messages,
          {
            id: crypto.randomUUID(),
            role: "assistant",
            content: "🛑 Sandbox stopped successfully.",
            timestamp: Date.now(),
          },
        ],
      }));
      setTimeLeft(null);
    } catch (error) {
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error: "Failed to stop sandbox",
      }));
    }
  };

  const handleExtendTimeout = async () => {
    if (!state.sandboxId) return;
    
    try {
      await increaseTimeout(state.sandboxId);
      setTimeLeft(300); // Reset to 5 minutes
    } catch (error) {
      console.error("Failed to extend timeout:", error);
    }
  };

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || state.isLoading || !state.sandboxId) return;

    const userMessage: SurfMessage = {
      id: crypto.randomUUID(),
      role: "user",
      content: input,
      timestamp: Date.now(),
    };

    setState((prev) => ({
      ...prev,
      messages: [...prev.messages, userMessage],
      isLoading: true,
    }));

    const currentInput = input;
    setInput("");

    // Cancel any existing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    abortControllerRef.current = new AbortController();

    try {
      const response = await fetch("/api/surf", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          messages: [{ role: "user", content: currentInput }],
          sandboxId: state.sandboxId,
          resolution: [1024, 768],
          model: "openai",
        }),
        signal: abortControllerRef.current.signal,
      });

      if (!response.ok) {
        throw new Error("Failed to send message");
      }

      const reader = response.body?.getReader();
      if (!reader) throw new Error("No response body");

      let assistantMessage = "";
      const assistantMessageId = crypto.randomUUID();

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = new TextDecoder().decode(value);
        const lines = chunk.split("\n");

        for (const line of lines) {
          if (line.startsWith("data: ")) {
            try {
              const event = JSON.parse(line.slice(6));
              
              if (event.type === SSEEventType.MESSAGE_CHUNK && event.content) {
                assistantMessage += event.content;
                
                setState((prev) => {
                  const newMessages = [...prev.messages];
                  const existingIndex = newMessages.findIndex(m => m.id === assistantMessageId);
                  
                  if (existingIndex >= 0) {
                    newMessages[existingIndex] = {
                      ...newMessages[existingIndex],
                      content: assistantMessage,
                    };
                  } else {
                    newMessages.push({
                      id: assistantMessageId,
                      role: "assistant",
                      content: assistantMessage,
                      timestamp: Date.now(),
                    });
                  }
                  
                  return { ...prev, messages: newMessages };
                });
              } else if (event.type === SSEEventType.ACTION_EXECUTED) {
                // Handle action feedback
                assistantMessage += `\n\n🔧 Executed: ${event.action?.action}`;
              } else if (event.type === SSEEventType.ERROR) {
                assistantMessage += `\n\n❌ Error: ${event.error}`;
              } else if (event.type === SSEEventType.DONE) {
                break;
              }
            } catch (error) {
              console.error("Failed to parse SSE event:", error);
            }
          }
        }
      }
    } catch (error) {
      if (error instanceof Error && error.name === "AbortError") {
        return; // Request was cancelled
      }
      
      setState((prev) => ({
        ...prev,
        messages: [
          ...prev.messages,
          {
            id: crypto.randomUUID(),
            role: "assistant",
            content: "❌ Sorry, I encountered an error. Please try again.",
            timestamp: Date.now(),
          },
        ],
      }));
    } finally {
      setState((prev) => ({ ...prev, isLoading: false }));
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-2">
          <Monitor className="w-5 h-5" />
          <span className="font-semibold">Surf Agent</span>
          {state.sandboxId && (
            <span className="text-xs text-muted-foreground">
              ID: {state.sandboxId.slice(0, 8)}...
            </span>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          {timeLeft !== null && (
            <div className="flex items-center gap-1 text-sm">
              <Clock className="w-4 h-4" />
              <span>{formatTime(timeLeft)}</span>
            </div>
          )}
          
          {!state.sandboxId ? (
            <Button
              onClick={handleStartSandbox}
              disabled={state.isLoading}
              size="sm"
            >
              {state.isLoading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Monitor className="w-4 h-4" />
              )}
              Start Sandbox
            </Button>
          ) : (
            <Button
              onClick={handleStopSandbox}
              disabled={state.isLoading}
              variant="destructive"
              size="sm"
            >
              <Square className="w-4 h-4" />
              Stop
            </Button>
          )}
        </div>
      </div>

      {/* VNC Display */}
      {state.vncUrl && (
        <div className="border-b bg-black">
          <iframe
            src={state.vncUrl}
            className="w-full h-64"
            title="Virtual Desktop"
          />
        </div>
      )}

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {state.messages.map((message) => (
          <div
            key={message.id}
            className={cn(
              "flex",
              message.role === "user" ? "justify-end" : "justify-start"
            )}
          >
            <div
              className={cn(
                "max-w-[80%] rounded-lg px-3 py-2",
                message.role === "user"
                  ? "bg-primary text-primary-foreground"
                  : "bg-muted"
              )}
            >
              <div className="text-sm whitespace-pre-wrap">{message.content}</div>
            </div>
          </div>
        ))}
        {state.isLoading && (
          <div className="flex justify-start">
            <div className="bg-muted rounded-lg px-3 py-2">
              <Loader2 className="w-4 h-4 animate-spin" />
            </div>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <form onSubmit={handleSendMessage} className="p-4 border-t">
        <div className="flex gap-2">
          <Input
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder={
              state.sandboxId
                ? "Tell me what to do on the desktop..."
                : "Start a sandbox first to begin chatting"
            }
            disabled={state.isLoading || !state.sandboxId}
            className="flex-1"
          />
          <Button
            type="submit"
            disabled={state.isLoading || !input.trim() || !state.sandboxId}
            size="icon"
          >
            <Send className="w-4 h-4" />
          </Button>
        </div>
      </form>

      {state.error && (
        <div className="p-4 bg-destructive/10 text-destructive text-sm">
          {state.error}
        </div>
      )}
    </div>
  );
}
