{"name": "surf", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ai-sdk/anthropic": "1.1.0", "@ai-sdk/google": "1.1.10", "@ai-sdk/groq": "1.1.9", "@ai-sdk/mistral": "1.1.11", "@ai-sdk/openai": "1.0.10", "@ai-sdk/ui-utils": "1.1.10", "@ai-sdk/xai": "1.1.10", "@anthropic-ai/sdk": "^0.39.0", "@e2b/desktop": "^1.8.1", "@gradio/client": "^1.10.0", "@phosphor-icons/react": "^2.1.7", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.5", "@radix-ui/react-separator": "^1.1.2", "@vercel/analytics": "^1.5.0", "@vercel/kv": "^3.0.0", "ai": "4.1.25", "ansis": "^3.17.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "e2b": "^1.3.0", "hls.js": "^1.5.19", "lucide-react": "^0.474.0", "motion": "^12.5.0", "next": "^15.2.4", "next-themes": "^0.4.4", "openai": "4.87.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^9.0.1", "react-use": "^17.6.0", "remark-gfm": "^4.0.0", "sharp": "^0.33.3", "sonner": "^1.7.2", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.1"}, "devDependencies": {"@tailwindcss/postcss": "^4.0.13", "@types/node": "^22.10.2", "@types/react": "^19.0.2", "@types/react-dom": "^19.0.2", "eslint": "^9.17.0", "eslint-config-next": "15.1.2", "postcss": "^8.4.49", "tailwindcss": "^4.0.13", "typescript": "^5.7.2"}}