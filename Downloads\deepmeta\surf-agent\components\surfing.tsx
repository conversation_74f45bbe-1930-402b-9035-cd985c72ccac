interface SurfingProps {
  className?: string;
}

export function Surfing({ className }: SurfingProps) {
  return (
    <pre className={className}>
      <code>{`
                       ..
                   .+....:
                   :~.~+=*
                  .:....~+       ..
              +++++**..=}}[())<)(:
          ~^)(][[[[[[}{{{)}}}]^
        -(]}[)-){#{{{{{{~
       <[{(   -)#######(
      ..^     *[{{{{#{}(<>^.
     ..       ^(](]}#{}[(<><)
              ++<><(   :){{{{.   .~:
              -=^*>)     .##{=.--   .~::
               *^><)  :===}#{-    ~::+*
              +^]][]===~..:*^-..-.-++
            -^({{{>=~ --    := :=++
           =^}}<=.:-.    :-.:~=++
         =~=-- -~     :~.:===++
      :++~ -...   .~- -====*~
    .~-:~-  :::-~-:~===++=
   -+~     :=~ ~====+*~
  .    :~---=++**+:
  ~+++=++**+:
        `}</code>
    </pre>
  );
}
